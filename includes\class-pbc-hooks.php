<?php
/**
 * WooCommerce integration hooks for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Hooks Class
 */
class PBC_Hooks {

    /**
     * Pricing engine instance
     *
     * @var PBC_Pricing_Engine
     */
    private $pricing_engine;

    /**
     * Country detector instance
     *
     * @var PBC_Country_Detector
     */
    private $country_detector;

    /**
     * Flag to prevent infinite loops during price calculation
     *
     * @var bool
     */
    private $calculating_price = false;

    /**
     * Constructor
     *
     * @param PBC_Pricing_Engine $pricing_engine Pricing engine instance
     * @param PBC_Country_Detector $country_detector Country detector instance
     */
    public function __construct($pricing_engine, $country_detector) {
        $this->pricing_engine = $pricing_engine;
        $this->country_detector = $country_detector;
        
        $this->init_hooks();
    }

    /**
     * Initialize WooCommerce hooks
     */
    private function init_hooks() {
        // Product price modification hooks
        add_filter('woocommerce_product_get_price', [$this, 'modify_product_price'], 10, 2);
        add_filter('woocommerce_product_get_regular_price', [$this, 'modify_product_regular_price'], 10, 2);
        add_filter('woocommerce_product_get_sale_price', [$this, 'modify_product_sale_price'], 10, 2);

        // Variable product pricing hooks
        add_filter('woocommerce_product_variation_get_price', [$this, 'modify_variation_price'], 10, 2);
        add_filter('woocommerce_product_variation_get_regular_price', [$this, 'modify_variation_regular_price'], 10, 2);
        add_filter('woocommerce_product_variation_get_sale_price', [$this, 'modify_variation_sale_price'], 10, 2);
        add_filter('woocommerce_variation_prices', [$this, 'modify_variation_prices'], 10, 3);

        // Cart and checkout hooks
        add_action('woocommerce_before_calculate_totals', [$this, 'update_cart_prices'], 10, 1);
        add_action('woocommerce_checkout_update_order_review', [$this, 'handle_checkout_address_change']);
        add_action('woocommerce_cart_calculate_fees', [$this, 'maybe_add_country_fee']);

        // Currency conversion hooks
        add_filter('woocommerce_currency', [$this, 'modify_woocommerce_currency'], 10, 1);
        add_filter('woocommerce_currency_symbol', [$this, 'modify_currency_symbol'], 10, 2);

        // AJAX hooks for real-time updates
        add_action('wp_ajax_pbc_update_country', [$this, 'ajax_update_country']);
        add_action('wp_ajax_nopriv_pbc_update_country', [$this, 'ajax_update_country']);
        add_action('wp_ajax_pbc_get_updated_prices', [$this, 'ajax_get_updated_prices']);
        add_action('wp_ajax_nopriv_pbc_get_updated_prices', [$this, 'ajax_get_updated_prices']);
        add_action('wp_ajax_pbc_update_checkout_prices', [$this, 'ajax_update_checkout_prices']);
        add_action('wp_ajax_nopriv_pbc_update_checkout_prices', [$this, 'ajax_update_checkout_prices']);

        // Frontend asset enqueuing
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);

        // Clear cache when products are updated
        add_action('woocommerce_update_product', [$this, 'clear_product_cache']);
        add_action('woocommerce_delete_product', [$this, 'clear_product_cache']);

        // Clear cache when categories are updated
        add_action('edited_product_cat', [$this, 'clear_category_cache']);
        add_action('delete_product_cat', [$this, 'clear_category_cache']);
    }

    /**
     * Modify simple product price
     *
     * @param string $price Product price
     * @param WC_Product $product Product object
     * @return string Modified price
     */
    public function modify_product_price($price, $product) {
        if ($this->calculating_price || !$this->should_modify_price($product)) {
            return $price;
        }

        $this->calculating_price = true;
        
        $product_id = $product->get_id();
        $country_code = $this->country_detector->detect_country();
        
        $price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code, floatval($price));
        
        $this->calculating_price = false;
        
        return strval($price_result['adjusted_price']);
    }

    /**
     * Modify product regular price
     *
     * @param string $price Regular price
     * @param WC_Product $product Product object
     * @return string Modified price
     */
    public function modify_product_regular_price($price, $product) {
        if ($this->calculating_price || !$this->should_modify_price($product)) {
            return $price;
        }

        $this->calculating_price = true;
        
        $product_id = $product->get_id();
        $country_code = $this->country_detector->detect_country();
        
        $price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code, floatval($price));
        
        $this->calculating_price = false;
        
        return strval($price_result['adjusted_price']);
    }

    /**
     * Modify product sale price
     *
     * @param string $price Sale price
     * @param WC_Product $product Product object
     * @return string Modified price
     */
    public function modify_product_sale_price($price, $product) {
        if ($this->calculating_price || !$this->should_modify_price($product) || empty($price)) {
            return $price;
        }

        $this->calculating_price = true;
        
        $product_id = $product->get_id();
        $country_code = $this->country_detector->detect_country();
        
        $price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code, floatval($price));
        
        $this->calculating_price = false;
        
        return strval($price_result['adjusted_price']);
    }

    /**
     * Modify variation price
     *
     * @param string $price Variation price
     * @param WC_Product_Variation $variation Variation object
     * @return string Modified price
     */
    public function modify_variation_price($price, $variation) {
        if ($this->calculating_price || !$this->should_modify_price($variation)) {
            return $price;
        }

        $this->calculating_price = true;
        
        $variation_id = $variation->get_id();
        $country_code = $this->country_detector->detect_country();
        
        $price_result = $this->pricing_engine->get_price_adjustment($variation_id, $country_code, floatval($price));
        
        $this->calculating_price = false;
        
        return strval($price_result['adjusted_price']);
    }

    /**
     * Modify variation regular price
     *
     * @param string $price Variation regular price
     * @param WC_Product_Variation $variation Variation object
     * @return string Modified price
     */
    public function modify_variation_regular_price($price, $variation) {
        if ($this->calculating_price || !$this->should_modify_price($variation)) {
            return $price;
        }

        $this->calculating_price = true;
        
        $variation_id = $variation->get_id();
        $country_code = $this->country_detector->detect_country();
        
        $price_result = $this->pricing_engine->get_price_adjustment($variation_id, $country_code, floatval($price));
        
        $this->calculating_price = false;
        
        return strval($price_result['adjusted_price']);
    }

    /**
     * Modify variation sale price
     *
     * @param string $price Variation sale price
     * @param WC_Product_Variation $variation Variation object
     * @return string Modified price
     */
    public function modify_variation_sale_price($price, $variation) {
        if ($this->calculating_price || !$this->should_modify_price($variation) || empty($price)) {
            return $price;
        }

        $this->calculating_price = true;
        
        $variation_id = $variation->get_id();
        $country_code = $this->country_detector->detect_country();
        
        $price_result = $this->pricing_engine->get_price_adjustment($variation_id, $country_code, floatval($price));
        
        $this->calculating_price = false;
        
        return strval($price_result['adjusted_price']);
    }

    /**
     * Modify variation prices array for variable products
     *
     * @param array $prices Prices array
     * @param WC_Product $product Product object
     * @param bool $display Whether for display or calculation
     * @return array Modified prices
     */
    public function modify_variation_prices($prices, $product, $display) {
        if ($this->calculating_price || !$this->should_modify_price($product)) {
            return $prices;
        }

        $this->calculating_price = true;
        
        $country_code = $this->country_detector->detect_country();
        
        // Modify regular prices
        if (isset($prices['regular_price'])) {
            foreach ($prices['regular_price'] as $variation_id => $price) {
                $price_result = $this->pricing_engine->get_price_adjustment($variation_id, $country_code, floatval($price));
                $prices['regular_price'][$variation_id] = $price_result['adjusted_price'];
            }
        }

        // Modify sale prices
        if (isset($prices['sale_price'])) {
            foreach ($prices['sale_price'] as $variation_id => $price) {
                if (!empty($price)) {
                    $price_result = $this->pricing_engine->get_price_adjustment($variation_id, $country_code, floatval($price));
                    $prices['sale_price'][$variation_id] = $price_result['adjusted_price'];
                }
            }
        }

        // Modify price array (current price)
        if (isset($prices['price'])) {
            foreach ($prices['price'] as $variation_id => $price) {
                $price_result = $this->pricing_engine->get_price_adjustment($variation_id, $country_code, floatval($price));
                $prices['price'][$variation_id] = $price_result['adjusted_price'];
            }
        }

        $this->calculating_price = false;
        
        return $prices;
    }

    /**
     * Update cart item prices before calculating totals
     *
     * @param WC_Cart $cart Cart object
     */
    public function update_cart_prices($cart) {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        if ($this->calculating_price) {
            return;
        }

        $this->calculating_price = true;
        
        $country_code = $this->country_detector->detect_country();

        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();

            // Skip if product shouldn't be modified
            if (!$this->should_modify_price($product)) {
                continue;
            }

            // Get original price (stored in product meta or use current price)
            $original_price = $this->get_original_product_price($product);
            
            // Calculate adjusted price
            $price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code, $original_price);
            
            // Set the new price
            $product->set_price($price_result['adjusted_price']);
        }

        $this->calculating_price = false;
    }

    /**
     * Handle checkout address change for real-time price updates
     */
    public function handle_checkout_address_change() {
        // Clear country detection cache to force re-detection
        $this->country_detector->clear_cache();
        
        // Clear pricing cache to ensure fresh calculations
        $this->pricing_engine->clear_all_price_cache();
        
        // Get the new country from posted data
        $new_country = $this->detect_country_from_checkout_data();
        
        if ($new_country) {
            // Store the detected country for immediate use
            $this->country_detector->set_forced_country($new_country);
        }
        
        // Trigger cart recalculation
        if (WC()->cart) {
            WC()->cart->calculate_totals();
        }
        
        // Log the address change for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("PBC: Checkout address changed to country: " . ($new_country ?: 'unknown'));
        }
    }

    /**
     * Detect country from checkout form data
     *
     * @return string|false Country code or false if not detected
     */
    private function detect_country_from_checkout_data() {
        // Check for shipping country first (higher priority)
        if (isset($_POST['shipping_country']) && !empty($_POST['shipping_country'])) {
            $country = sanitize_text_field($_POST['shipping_country']);
            if ($this->country_detector->is_valid_country_code($country)) {
                return strtoupper($country);
            }
        }

        // Check for billing country as fallback
        if (isset($_POST['billing_country']) && !empty($_POST['billing_country'])) {
            $country = sanitize_text_field($_POST['billing_country']);
            if ($this->country_detector->is_valid_country_code($country)) {
                return strtoupper($country);
            }
        }

        return false;
    }

    /**
     * Maybe add country-specific fees (for future use)
     *
     * @param WC_Cart $cart Cart object
     */
    public function maybe_add_country_fee($cart) {
        // This method is reserved for future country-specific fee functionality
        // Currently not implemented as it's not in the requirements
    }

    /**
     * AJAX handler for country update
     */
    public function ajax_update_country() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pbc_update_country')) {
            wp_die('Security check failed');
        }

        $country_code = sanitize_text_field($_POST['country_code']);
        
        if (!$this->country_detector->is_valid_country_code($country_code)) {
            wp_send_json_error('Invalid country code');
        }

        // Clear caches
        $this->country_detector->clear_cache();
        $this->pricing_engine->clear_all_price_cache();

        // Store the new country in session for forced detection
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        $_SESSION['pbc_forced_country'] = $country_code;
        
        wp_send_json_success([
            'country_code' => $country_code,
            'message' => 'Country updated successfully'
        ]);
    }

    /**
     * AJAX handler for getting updated prices
     */
    public function ajax_get_updated_prices() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pbc_update_country')) {
            wp_die('Security check failed');
        }

        $product_ids = array_map('intval', $_POST['product_ids']);
        $country_code = sanitize_text_field($_POST['country_code']);

        if (empty($product_ids) || !$this->country_detector->is_valid_country_code($country_code)) {
            wp_send_json_error('Invalid parameters');
        }

        $updated_prices = [];

        foreach ($product_ids as $product_id) {
            $product = wc_get_product($product_id);
            if (!$product) {
                continue;
            }

            // Get price adjustment
            $price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code);
            
            // Format prices for display
            $updated_prices[$product_id] = [
                'price' => $price_result['adjusted_price'],
                'price_html' => wc_price($price_result['adjusted_price']),
                'regular_price' => $price_result['adjusted_price'],
                'regular_price_html' => wc_price($price_result['adjusted_price']),
                'currency' => get_woocommerce_currency(),
                'country_code' => $country_code,
                'rule_source' => $price_result['rule_source']
            ];

            // Handle sale prices if applicable
            if ($product->is_on_sale()) {
                $sale_price = $product->get_sale_price();
                if ($sale_price) {
                    $sale_price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code, floatval($sale_price));
                    $updated_prices[$product_id]['sale_price'] = $sale_price_result['adjusted_price'];
                    $updated_prices[$product_id]['sale_price_html'] = wc_price($sale_price_result['adjusted_price']);
                    $updated_prices[$product_id]['price'] = $sale_price_result['adjusted_price'];
                    $updated_prices[$product_id]['price_html'] = wc_price($sale_price_result['adjusted_price']);
                }
            }
        }

        wp_send_json_success($updated_prices);
    }

    /**
     * AJAX handler for checkout price updates
     */
    public function ajax_update_checkout_prices() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'pbc_update_country')) {
            wp_die('Security check failed');
        }

        $billing_country = sanitize_text_field($_POST['billing_country'] ?? '');
        $shipping_country = sanitize_text_field($_POST['shipping_country'] ?? '');

        // Determine which country to use (shipping takes priority)
        $country_code = $shipping_country ?: $billing_country;

        if (!$this->country_detector->is_valid_country_code($country_code)) {
            wp_send_json_error('Invalid country code');
        }

        // Clear caches
        $this->country_detector->clear_cache();
        $this->pricing_engine->clear_all_price_cache();

        // Set the forced country
        $this->country_detector->set_forced_country($country_code);

        // Get cart items and calculate new prices
        $cart_data = [];
        $total_change = 0;

        if (WC()->cart && !WC()->cart->is_empty()) {
            foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                $product = $cart_item['data'];
                $product_id = $product->get_id();
                $quantity = $cart_item['quantity'];

                // Get original price
                $original_price = $this->get_original_product_price($product);
                
                // Get adjusted price
                $price_result = $this->pricing_engine->get_price_adjustment($product_id, $country_code, $original_price);
                
                $item_total_change = ($price_result['adjusted_price'] - $original_price) * $quantity;
                $total_change += $item_total_change;

                $cart_data[$cart_item_key] = [
                    'product_id' => $product_id,
                    'original_price' => $original_price,
                    'adjusted_price' => $price_result['adjusted_price'],
                    'price_change' => $price_result['adjusted_price'] - $original_price,
                    'item_total_change' => $item_total_change,
                    'quantity' => $quantity,
                    'rule_source' => $price_result['rule_source']
                ];
            }

            // Recalculate cart totals
            WC()->cart->calculate_totals();
        }

        wp_send_json_success([
            'country_code' => $country_code,
            'cart_data' => $cart_data,
            'total_change' => $total_change,
            'formatted_total_change' => wc_price($total_change),
            'new_cart_total' => WC()->cart ? WC()->cart->get_total('') : 0,
            'formatted_cart_total' => WC()->cart ? WC()->cart->get_total() : wc_price(0),
            'message' => 'Checkout prices updated for ' . $country_code
        ]);
    }

    /**
     * Modify WooCommerce currency based on country detection
     *
     * @param string $currency Current currency code
     * @return string Modified currency code
     */
    public function modify_woocommerce_currency($currency) {
        // Only modify if currency conversion is enabled
        if (!get_option('pbc_enable_currency_conversion', false)) {
            return $currency;
        }

        // Get detected country
        $country_code = $this->country_detector->detect_country();
        if (!$country_code) {
            return $currency;
        }

        // Get target currency for country
        $target_currency = $this->pricing_engine->get_country_currency($country_code);

        // Return target currency if different from base
        if ($target_currency && $target_currency !== $currency) {
            return $target_currency;
        }

        return $currency;
    }

    /**
     * Modify currency symbol based on country detection
     *
     * @param string $currency_symbol Current currency symbol
     * @param string $currency Currency code
     * @return string Modified currency symbol
     */
    public function modify_currency_symbol($currency_symbol, $currency) {
        // Only modify if currency conversion is enabled
        if (!get_option('pbc_enable_currency_conversion', false)) {
            return $currency_symbol;
        }

        // Get detected country
        $country_code = $this->country_detector->detect_country();
        if (!$country_code) {
            return $currency_symbol;
        }

        // Get target currency for country
        $target_currency = $this->pricing_engine->get_country_currency($country_code);

        // Return appropriate symbol for target currency
        if ($target_currency && $target_currency !== get_option('woocommerce_currency')) {
            return get_woocommerce_currency_symbol($target_currency);
        }

        return $currency_symbol;
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only enqueue on WooCommerce pages
        if (!is_woocommerce() && !is_cart() && !is_checkout() && !is_account_page()) {
            return;
        }

        $plugin_url = plugin_dir_url(dirname(__FILE__));
        $plugin_version = defined('PBC_VERSION') ? PBC_VERSION : '1.0.0';

        // Enqueue CSS
        wp_enqueue_style(
            'pbc-frontend',
            $plugin_url . 'public/css/pbc-frontend.css',
            [],
            $plugin_version
        );

        // Enqueue JavaScript
        wp_enqueue_script(
            'pbc-frontend',
            $plugin_url . 'public/js/pbc-frontend.js',
            ['jquery'],
            $plugin_version,
            true
        );

        // Localize script with necessary data
        wp_localize_script('pbc-frontend', 'pbc_frontend_vars', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('pbc_update_country'),
            'current_country' => $this->country_detector->detect_country(),
            'currency' => get_woocommerce_currency(),
            'currency_symbol' => get_woocommerce_currency_symbol(),
            'price_format' => get_woocommerce_price_format(),
            'debug' => defined('WP_DEBUG') && WP_DEBUG
        ]);
    }

    /**
     * Clear product cache when product is updated
     *
     * @param int $product_id Product ID
     */
    public function clear_product_cache($product_id) {
        $this->pricing_engine->clear_price_cache($product_id);
        
        // Also clear WooCommerce product cache
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients($product_id);
        }
    }

    /**
     * Clear category cache when category is updated
     *
     * @param int $category_id Category ID
     */
    public function clear_category_cache($category_id) {
        // Get all products in this category
        $products = get_posts([
            'post_type' => 'product',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'tax_query' => [
                [
                    'taxonomy' => 'product_cat',
                    'field' => 'term_id',
                    'terms' => $category_id
                ]
            ]
        ]);

        // Clear cache for all products in the category
        foreach ($products as $product_id) {
            $this->pricing_engine->clear_price_cache($product_id);
        }
    }

    /**
     * Check if price should be modified for this product
     *
     * @param WC_Product $product Product object
     * @return bool True if should modify, false otherwise
     */
    private function should_modify_price($product) {
        // Don't modify prices in admin unless it's an AJAX request
        if (is_admin() && !defined('DOING_AJAX')) {
            return false;
        }

        // Don't modify if product is not valid
        if (!$product || !$product->get_id()) {
            return false;
        }

        // Don't modify if WooCommerce is not fully loaded
        if (!function_exists('WC') || !WC()->cart) {
            return false;
        }

        // Allow filtering of which products should have modified prices
        return apply_filters('pbc_should_modify_product_price', true, $product);
    }

    /**
     * Get original product price (before any modifications)
     *
     * @param WC_Product $product Product object
     * @return float Original price
     */
    private function get_original_product_price($product) {
        // Try to get original price from meta first
        $original_price = $product->get_meta('_pbc_original_price');
        
        if ($original_price) {
            return floatval($original_price);
        }

        // Fallback to current price (this might already be modified)
        $price = $product->get_price();
        
        // Store original price for future reference
        $product->update_meta_data('_pbc_original_price', $price);
        
        return floatval($price);
    }

    /**
     * Get pricing engine instance (for external access)
     *
     * @return PBC_Pricing_Engine
     */
    public function get_pricing_engine() {
        return $this->pricing_engine;
    }

    /**
     * Get country detector instance (for external access)
     *
     * @return PBC_Country_Detector
     */
    public function get_country_detector() {
        return $this->country_detector;
    }

    /**
     * Check if currently calculating prices (to prevent infinite loops)
     *
     * @return bool
     */
    public function is_calculating_price() {
        return $this->calculating_price;
    }

    /**
     * Temporarily disable price modifications
     */
    public function disable_price_modifications() {
        $this->calculating_price = true;
    }

    /**
     * Re-enable price modifications
     */
    public function enable_price_modifications() {
        $this->calculating_price = false;
    }

    /**
     * Get current country for debugging
     *
     * @return string Current country code
     */
    public function get_current_country() {
        return $this->country_detector->detect_country();
    }

    /**
     * Force refresh all prices (useful for debugging or manual triggers)
     */
    public function refresh_all_prices() {
        $this->country_detector->clear_cache();
        $this->pricing_engine->clear_all_price_cache();
        
        if (WC()->cart) {
            WC()->cart->calculate_totals();
        }
    }
}